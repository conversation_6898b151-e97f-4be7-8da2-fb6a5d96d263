name: Update yt-dlp

on:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch: # Allow manual trigger

permissions:
  contents: write
  pull-requests: write

jobs:
  check-ytdlp-update:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install requests

    - name: Check for yt-dlp updates
      id: check-update
      run: |
        # Get current version from requirements.txt
        CURRENT_VERSION=$(grep "yt-dlp>=" backend/requirements.txt | sed 's/yt-dlp>=//')
        echo "Current version: $CURRENT_VERSION"
        echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
        
        # Get latest version from PyPI
        LATEST_VERSION=$(python -c "
        import requests
        response = requests.get('https://pypi.org/pypi/yt-dlp/json')
        data = response.json()
        print(data['info']['version'])
        ")
        echo "Latest version: $LATEST_VERSION"
        
        # Compare versions
        if [ "$CURRENT_VERSION" != "$LATEST_VERSION" ]; then
          echo "update_available=true" >> $GITHUB_OUTPUT
          echo "new_version=$LATEST_VERSION" >> $GITHUB_OUTPUT
          echo "Update available: $CURRENT_VERSION -> $LATEST_VERSION"
        else
          echo "update_available=false" >> $GITHUB_OUTPUT
          echo "No update available"
        fi

    - name: Update requirements.txt
      if: steps.check-update.outputs.update_available == 'true'
      run: |
        # Update yt-dlp version in requirements.txt
        sed -i "s/yt-dlp>=.*/yt-dlp>=${{ steps.check-update.outputs.new_version }}/" backend/requirements.txt

    - name: Create branch and push changes
      if: steps.check-update.outputs.update_available == 'true'
      run: |
        # Generate unique branch name with timestamp
        TIMESTAMP=$(date +%Y%m%d-%H%M%S)
        BRANCH_NAME="auto-update-ytdlp-${{ steps.check-update.outputs.new_version }}-${TIMESTAMP}"
        echo "branch_name=${BRANCH_NAME}" >> $GITHUB_OUTPUT

        # Create and switch to new branch
        git checkout -b ${BRANCH_NAME}

        # Configure git
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

        # Add and commit changes
        git add backend/requirements.txt
        git commit -m "chore: update yt-dlp to ${{ steps.check-update.outputs.new_version }}"

        # Push the branch
        git push origin ${BRANCH_NAME}

        # Output information for manual PR creation
        echo "::notice title=Update Available::yt-dlp update available from ${{ steps.check-update.outputs.current_version }} to ${{ steps.check-update.outputs.new_version }}"
        echo "::notice title=Branch Created::Branch '${BRANCH_NAME}' has been created and pushed"
        echo "::notice title=Manual Action Required::Please create a pull request manually from the branch '${BRANCH_NAME}' to 'dev'"
      id: create-branch
    - name: Create Pull Request to dev branch
      if: steps.check-update.outputs.update_available == 'true'
      run: |
        BRANCH_NAME="${{ steps.create-branch.outputs.branch_name }}"

        # Create PR using GitHub CLI
        gh pr create \
          --title "🤖 Auto-update yt-dlp to ${{ steps.check-update.outputs.new_version }}" \
          --body "## 🤖 Automated yt-dlp Update (Testing in dev)

        This PR updates yt-dlp from ${{ steps.check-update.outputs.current_version }} to ${{ steps.check-update.outputs.new_version }}.

        ### Changes:
        - Updated yt-dlp version in \`backend/requirements.txt\`

        ### Testing Workflow:
        1. ✅ Merge this PR to \`dev\` branch
        2. 🧪 Test the updated version in dev environment
        3. 📝 Create PR from \`dev\` to \`main\` when testing is complete

        ### Testing Checklist:
        - [ ] Verify downloads still work correctly
        - [ ] Test with various video platforms (YouTube, etc.)
        - [ ] Check for any breaking changes
        - [ ] Verify backend functionality

        ---
        *This PR targets the \`dev\` branch for testing before merging to \`main\`.*" \
          --base dev \
          --head ${BRANCH_NAME}
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}